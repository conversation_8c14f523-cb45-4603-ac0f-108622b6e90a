# Teams Authentication Production Fix

## Problem Analysis

The Teams authentication is failing in production with HTTP 400 error because of missing Azure App Registration configuration. The error occurs when trying to access:
```
https://accuremd.azurewebsites.net/api/auth/start-teams-login?userId=5f2cd7...&redirectUri=https%3A%2F%2Faccuremd.azurewebsites.net%2Fhtml%2Fauth-callback.html
```

## Root Causes

1. **Missing Redirect URI in Azure App Registration**: The redirect URI `https://accuremd.azurewebsites.net/html/auth-callback.html` is not registered in your Azure App Registration.

2. **Missing CORS Configuration**: Teams authentication requires proper CORS setup for cross-origin requests.

3. **Missing Teams-specific permissions**: The app may not have the correct permissions configured in Azure.

## Azure App Registration Configuration Steps

### Step 1: Add Redirect URIs

1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to **Azure Active Directory** > **App registrations**
3. Find your app with ID: `24a397f4-16dd-4dae-8b8f-5368c3a81fed`
4. Go to **Authentication** section
5. Under **Redirect URIs**, add these URIs:
   ```
   https://accuremd.azurewebsites.net/html/auth-callback.html
   https://accuremd.azurewebsites.net/api/auth/callback
   https://accuremd.azurewebsites.net/html/auth-start.html
   ```
6. Set **Redirect URI type** to **Web**
7. Click **Save**

### Step 2: Configure API Permissions

1. In your app registration, go to **API permissions**
2. Ensure these permissions are granted:
   ```
   Microsoft Graph:
   - User.Read (Delegated)
   - OnlineMeetings.ReadWrite (Delegated)
   - Calendars.Read (Delegated)
   - openid (Delegated)
   - profile (Delegated)
   - email (Delegated)
   ```
3. Click **Grant admin consent** for your tenant

### Step 3: Configure Authentication Settings

1. In **Authentication** section:
   - Enable **Access tokens** (used for implicit flows)
   - Enable **ID tokens** (used for OpenID Connect flows)
   - Under **Supported account types**, ensure it's set to **Single tenant** (matches your config)

### Step 4: Configure Teams App Manifest

1. Update your Teams app manifest to include the correct domains:
   ```json
   "validDomains": [
     "accuremd.azurewebsites.net",
     "login.microsoftonline.com",
     "graph.microsoft.com"
   ]
   ```

### Step 5: Verify Client Secret

1. In **Certificates & secrets** section
2. Ensure your client secret `****************************************` is still valid
3. If expired, create a new one and update your configuration

## Code Changes Made

### 1. Added CORS Configuration (Program.cs)
```csharp
// Add CORS for Teams authentication
builder.Services.AddCors(options =>
{
    options.AddPolicy("TeamsPolicy", policy =>
    {
        policy.WithOrigins(
            "https://teams.microsoft.com",
            "https://teams.microsoft.us",
            "https://login.microsoftonline.com",
            "https://accuremd.azurewebsites.net"
        )
        .AllowAnyMethod()
        .AllowAnyHeader()
        .AllowCredentials();
    });
});
```

### 2. Enhanced Error Handling (AuthenticationService.cs)
- Added redirect URI validation
- Added HTTPS enforcement for production
- Better error logging

### 3. Improved Logging (AuthController.cs)
- Added configuration logging for debugging
- Enhanced error messages
- Better parameter validation

## Testing Steps

1. **Deploy the updated code** to Azure App Service
2. **Test the configuration endpoint**:
   ```
   GET https://accuremd.azurewebsites.net/api/auth/test
   ```
3. **Test Teams authentication flow**:
   - Open your Teams app
   - Try to authenticate
   - Check the browser console for detailed logs

## Debugging Commands

If issues persist, check these endpoints:

1. **Configuration test**:
   ```
   curl https://accuremd.azurewebsites.net/api/auth/test
   ```

2. **Manual auth URL generation**:
   ```
   curl "https://accuremd.azurewebsites.net/api/auth/start-teams-login?userId=test&redirectUri=https%3A%2F%2Faccuremd.azurewebsites.net%2Fhtml%2Fauth-callback.html"
   ```

## Expected Results

After implementing these fixes:
- Teams authentication should redirect properly to Microsoft login
- Users should be able to complete the OAuth flow
- The callback should work correctly
- Authentication status should persist in the database

## Common Issues and Solutions

1. **Still getting 400 error**: Double-check redirect URIs in Azure App Registration
2. **CORS errors**: Ensure all Teams domains are in CORS policy
3. **Token validation errors**: Verify client secret and tenant ID
4. **Callback not working**: Check that auth-callback.html is accessible

## Next Steps

1. Apply Azure App Registration changes
2. Deploy the code updates
3. Test the authentication flow
4. Monitor logs for any remaining issues
